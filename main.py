import pandas as pd
from datetime import datetime

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    wip_list = pd.read_excel('WIP.xlsx')

# 预定义字段
grade_priority = ['P', 'S', 'T', 'I', 'G', 'R',]
hold_state_priority = ['OnHold', 'NotOnHold',]

def get_highest_priority(values, priority_list):
    """根据优先级列表获取最高优先级的值"""
    for priority in priority_list:
        if priority in values:
            return priority
    return None

# 按CARRIERNAME分组处理
carrier_summary = []

for carrier_name, group in wip_list.groupby('CARRIERNAME'):
    # ①计算carrier等级
    grades = group['PRODUCTGRADE'].dropna().unique()
    carrier_grade = get_highest_priority(grades, grade_priority)

    # ②计算hold状态
    hold_states = group['PRODUCTHOLDSTATE'].dropna().unique()
    carrier_hold_state = get_highest_priority(hold_states, hold_state_priority)

    # ③计算carrier用量（行数）
    carrier_count = len(group)

    # ④计算carrier最新事件
    latest_event = group.loc[group['LASTEVENTTIME'].idxmax()]

    # ⑤计算IDLE时间
    latest_idle_time = group['LASTIDLETIME'].max()
    current_time = datetime.now()
    idle_hours = round((current_time - pd.to_datetime(latest_idle_time)).total_seconds() / 3600, 1)

    carrier_summary.append({
        'Product ID': group['PRODUCTSPECNAME'].iloc[0],  # 源数据同一carrier只有一个PRODUCTSPECNAME
        'CST ID': carrier_name,
        'Lot Grade': carrier_grade,
        'Hold State': carrier_hold_state,
        'Qty': carrier_count,
        'Stay Time(H)': idle_hours,
        'LASTEVENTNAME': latest_event['LASTEVENTNAME'],
        'LASTEVENTTIME': latest_event['LASTEVENTTIME'],
        'LASTEVENTUSER': latest_event['LASTEVENTUSER'],
        'LASTEVENTCOMMENT': latest_event['LASTEVENTCOMMENT'],
    })

# 转换为DataFrame
carrier_df = pd.DataFrame(carrier_summary)

# 按Product ID、CST ID排序
carrier_df.sort_values(by=['Product ID', 'CST ID'], inplace=True)

# 显式定义列类型
carrier_df = carrier_df.astype({
    'Product ID': 'string',
    'CST ID': 'string',
    'Lot Grade': 'string',
    'Hold State': 'string',
    'Qty': 'int64',
    'Stay Time(H)': 'float64',
    'LASTEVENTNAME': 'string',
    'LASTEVENTTIME': 'datetime64[ns]',
    'LASTEVENTUSER': 'string',
    'LASTEVENTCOMMENT': 'string'
})

time_trigger = datetime.now()

if platform == 'ide':
    carrier_df.to_excel('carrier_summary.xlsx', index=False)
