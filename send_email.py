import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from Spotfire.Dxp.Data import *
from email.utils import formataddr
import base64

# 邮件配置
MAIL_HOST = "*************"
MAIL_PORT = 25
SENDER = "<EMAIL>"
SENDER_NAME = "厦门G8.6基地新品新技术导入部CF新品导入科"
# 测试账号
RECEIVERS = ['<EMAIL>']
RECEIVERS_NAMES = ['LaiWeiXuan赖伟煊']
# RECEIVERS = ['<EMAIL>',]
# RECEIVERS_NAMES = ['厦门G8.6基地新品新技术导入部CF新品导入科',]


def Header(string):
    """编码中文字符串用于邮件头部"""
    b64 = base64.b64encode(string.encode('utf-8'))
    hdr = '=?utf-8?b?{}?='.format(b64)
    return hdr


def try_parse_datetime(date_string):
    """尝试解析不同格式的日期字符串"""
    formats = [
        "%Y-%m-%d %H:%M:%S",  # 本地格式 2025-2-28 12:01:24
        "%m/%d/%Y %I:%M:%S %p"  # 服务器格式 2/28/2025 12:01:24 PM
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_string, fmt)
        except ValueError:
            continue
    return None


def normalize_datetime_format(date_string):
    """将不同格式的日期字符串统一转换为 'YYYY-MM-DD HH:MM' 格式"""
    dt = try_parse_datetime(date_string)
    if dt:
        return dt.strftime("%Y-%m-%d %H:%M")
    return date_string


def get_carrier_records():
    """获取Carrier数据表记录并进行过滤"""
    try:
        data_table = Document.Data.Tables["Carrier"]
        data_table.ReloadAllData()

        # 定义所需列
        column_names = [
            "Product ID",
            "CST ID",
            "Lot Grade",
            "Hold State",
            "Qty",
            "Stay Time(H)",
            "LASTEVENTNAME",
            "LASTEVENTTIME",
            "LASTEVENTUSER",
            "LASTEVENTCOMMENT"
        ]

        # 创建列游标字典
        cursors = {}
        for col_name in column_names:
            cursors[col_name] = DataValueCursor.CreateFormatted(data_table.Columns[col_name])

        records = []
        rows_to_include = IndexSet(data_table.RowCount, True)

        # 收集唯一值用于颜色映射
        unique_product_ids = set()
        unique_lot_grades = set()
        unique_hold_states = set()

        # 使用GetRows获取所有列的数据
        for row in data_table.GetRows(rows_to_include, *cursors.values()):
            product_id = cursors["Product ID"].CurrentValue
            cst_id = cursors["CST ID"].CurrentValue
            lot_grade = cursors["Lot Grade"].CurrentValue
            hold_state = cursors["Hold State"].CurrentValue
            qty_str = cursors["Qty"].CurrentValue
            stay_time_str = cursors["Stay Time(H)"].CurrentValue
            last_event_name = cursors["LASTEVENTNAME"].CurrentValue
            last_event_time_raw = cursors["LASTEVENTTIME"].CurrentValue
            last_event_time = normalize_datetime_format(last_event_time_raw)
            last_event_user = cursors["LASTEVENTUSER"].CurrentValue
            last_event_comment = cursors["LASTEVENTCOMMENT"].CurrentValue[:50]
            
            # 转换数值类型
            try:
                qty = int(float(qty_str)) if qty_str else 0
            except:
                qty = 0
                
            try:
                stay_time = float(stay_time_str) if stay_time_str else 0.0
            except:
                stay_time = 0.0

            # 过滤条件：(Lot Grade列为R) 或 (Lot Grade为P 且 Qty≤3)
            if lot_grade == 'R' or (lot_grade == 'P' and qty <= 3):
                unique_product_ids.add(product_id)
                unique_lot_grades.add(lot_grade)
                unique_hold_states.add(hold_state)

                record = {
                    'product_id': product_id,
                    'cst_id': cst_id,
                    'lot_grade': lot_grade,
                    'hold_state': hold_state,
                    'qty': qty,
                    'stay_time': stay_time,
                    'last_event_name': last_event_name,
                    'last_event_time': last_event_time,
                    'last_event_user': last_event_user,
                    'last_event_comment': last_event_comment
                }
                records.append(record)

        # 如果没有符合条件的记录，返回空列表
        if not records:
            return []

        # 创建颜色映射
        # Product ID 颜色
        product_id_colors = ['#FFE6E6', '#E6F3FF', '#E6FFE6', '#FFF0E6', '#F0E6FF', '#E6FFFF']
        product_id_color_map = {pid: product_id_colors[i % len(product_id_colors)] 
                               for i, pid in enumerate(sorted(unique_product_ids))}

        # Lot Grade 颜色
        lot_grade_colors = ['#FFB6C1', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA']
        lot_grade_color_map = {lg: lot_grade_colors[i % len(lot_grade_colors)] 
                              for i, lg in enumerate(sorted(unique_lot_grades))}

        # Hold State 颜色
        hold_state_colors = ['#FFA07A', '#90EE90', '#ADD8E6', '#D8BFD8', '#FAFAD2', '#B0C4DE']
        hold_state_color_map = {hs: hold_state_colors[i % len(hold_state_colors)] 
                               for i, hs in enumerate(sorted(unique_hold_states))}

        # 计算Stay Time的最大值和最小值用于颜色渐变
        stay_times = [record['stay_time'] for record in records]
        max_stay_time = max(stay_times) if stay_times else 0
        min_stay_time = min(stay_times) if stay_times else 0
        stay_time_range = max_stay_time - min_stay_time

        # 添加颜色信息到记录中
        for record in records:
            record['product_id_color'] = product_id_color_map[record['product_id']]
            record['lot_grade_color'] = lot_grade_color_map[record['lot_grade']]
            record['hold_state_color'] = hold_state_color_map[record['hold_state']]
            
            # Qty颜色渐变（最大值固定为26）
            qty_normalized = record['qty'] / 26.0 if record['qty'] <= 26 else 1.0
            qty_lightness = 90 - (qty_normalized * 50)  # 90%到40%的亮度渐变
            record['qty_color'] = "hsl(200, 50%, {}%)".format(qty_lightness)
            
            # Stay Time颜色渐变
            if stay_time_range == 0:
                stay_time_lightness = 70
            else:
                stay_time_normalized = (record['stay_time'] - min_stay_time) / stay_time_range
                stay_time_lightness = 90 - (stay_time_normalized * 50)
            record['stay_time_color'] = "hsl(120, 50%, {}%)".format(stay_time_lightness)

        return records

    except Exception as e:
        print("Get carrier records failed: {}".format(str(e)))
        return None


def create_email_message(records):
    """创建邮件内容"""
    try:
        message = MIMEMultipart()

        # 创建HTML表格样式
        html_content = """
        <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; }
                    table {
                        border-collapse: collapse;
                        width: 100%;
                        margin-top: 20px;
                    }
                    th {
                        background-color: #4A90E2;
                        color: white;
                        padding: 6px;
                        text-align: center;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 6px;
                        border: 1px solid #ddd;
                        text-align: center;
                        max-width: 120px;
                        word-wrap: break-word;
                    }
                    .note {
                        color: #666;
                        font-size: 13px;
                        margin: 5px 0;
                        line-height: 1.4;
                    }
                    .notes-container {
                        background-color: #f9f9f9;
                        padding: 10px;
                        border-left: 3px solid #4A90E2;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <h3>P等/R等卡夹信息如下：</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Product ID</th>
                            <th>CST ID</th>
                            <th>Lot Grade</th>
                            <th>Hold State</th>
                            <th>Qty</th>
                            <th>Stay Time(H)</th>
                            <th>LASTEVENTNAME</th>
                            <th>LASTEVENTTIME</th>
                            <th>LASTEVENTUSER</th>
                            <th>LASTEVENTCOMMENT</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # 添加数据行
        for record in records:
            row_html = """
                <tr>
                    <td style="background-color: {product_id_color}">{product_id}</td>
                    <td>{cst_id}</td>
                    <td style="background-color: {lot_grade_color}">{lot_grade}</td>
                    <td style="background-color: {hold_state_color}">{hold_state}</td>
                    <td style="background-color: {qty_color}">{qty}</td>
                    <td style="background-color: {stay_time_color}">{stay_time:.1f}</td>
                    <td>{last_event_name}</td>
                    <td>{last_event_time}</td>
                    <td>{last_event_user}</td>
                    <td>{last_event_comment}</td>
                </tr>
            """.format(
                product_id_color=record['product_id_color'],
                product_id=record['product_id'],
                cst_id=record['cst_id'],
                lot_grade_color=record['lot_grade_color'],
                lot_grade=record['lot_grade'],
                hold_state_color=record['hold_state_color'],
                hold_state=record['hold_state'],
                qty_color=record['qty_color'],
                qty=record['qty'],
                stay_time_color=record['stay_time_color'],
                stay_time=record['stay_time'],
                last_event_name=record['last_event_name'],
                last_event_time=record['last_event_time'],
                last_event_user=record['last_event_user'],
                last_event_comment=record['last_event_comment']
            )

            html_content += row_html

        html_content += """
                    </tbody>
                </table>
                <div class="notes-container">
                    <p class="note">说明：</p>
                    <p class="note">① 仅包含IDLE卡夹</p>
                    <p class="note">② P等卡夹过滤条件：gls数量≤3。避免Highlight待重投玻璃</p>
                </div>
                <p style="color: #666; font-size: 12px;">本邮件为自动发送，无需回复</p>
            </body>
        </html>
        """

        message.attach(MIMEText(html_content, "html", "utf-8"))

        # 设置邮件头部信息
        message["From"] = formataddr((Header(SENDER_NAME), SENDER))

        # 构建收件人列表
        recipients = []
        for i, receiver in enumerate(RECEIVERS):
            if i < len(RECEIVERS_NAMES):
                recipients.append(formataddr((Header(RECEIVERS_NAMES[i]), receiver)))
            else:
                recipients.append(receiver)

        message["To"] = ", ".join(recipients)

        date_str = datetime.now().strftime("%Y-%m-%d")
        full_subject = "{} - {}".format(Header("卡夹处理提醒"), date_str)
        message["Subject"] = full_subject

        return message

    except Exception as e:
        print("Create message failed: {}".format(e))
        return None


def send_email(records):
    """发送电子邮件"""
    try:
        # 建立SMTP连接
        smtp_obj = smtplib.SMTP(MAIL_HOST, MAIL_PORT)
        smtp_obj.starttls()
        smtp_obj.ehlo_or_helo_if_needed()

        # 创建邮件消息
        message = create_email_message(records)
        if not message:
            return False

        # 发送邮件
        smtp_obj.sendmail(SENDER, RECEIVERS, message.as_string())
        print("Email sent successfully")
        return True

    except Exception as e:
        print("Send email failed: {}".format(e))
        return False

    finally:
        try:
            smtp_obj.quit()
        except:
            pass


def main():
    """主函数"""
    try:
        print("Starting execution...")

        # 获取记录
        records = get_carrier_records()
        if records is None:
            print("Failed to get records")
            return
            
        if len(records) == 0:
            print("No records match the filter criteria, email will not be sent")
            return

        print("Found {} records matching filter criteria".format(len(records)))

        # 发送邮件
        if send_email(records):
            print("Process completed successfully")
        else:
            print("Failed to send email")

    except Exception as e:
        print("Execution failed: {}".format(e))


# 执行主函数
main()
