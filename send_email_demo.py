import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from Spotfire.Dxp.Data import *
from email.utils import formataddr
import base64

# 邮件配置
MAIL_HOST = "*************"
MAIL_PORT = 25
SENDER = "<EMAIL>"
SENDER_NAME = "厦门G8.6基地新品新技术导入部CF新品导入科"
# 测试账号
# RECEIVERS = ['<EMAIL>']
# RECEIVERS_NAMES = ['LaiWeiXuan赖伟煊']
RECEIVERS = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
RECEIVERS_NAMES = ['厦门G8.6基地新品新技术导入部CF新品导入科', '<PERSON><PERSON><PERSON>g<PERSON><PERSON>李祥国', 'LiuLinYing刘林英']


def Header(string):
    b64 = base64.b64encode(string.encode('utf-8'))
    hdr = '=?utf-8?b?{}?='.format(b64)
    return hdr


def try_parse_datetime(date_string):
    """尝试解析不同格式的日期字符串"""
    formats = [
        "%Y-%m-%d %H:%M:%S",  # 本地格式 2025-2-28 12:01:24
        "%m/%d/%Y %I:%M:%S %p"  # 服务器格式 2/28/2025 12:01:24 PM
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_string, fmt)
        except ValueError:
            continue
    return None


def normalize_datetime_format(date_string):
    """将不同格式的日期字符串统一转换为 'YYYY-MM-DD HH:MM' 格式"""
    dt = try_parse_datetime(date_string)
    if dt:
        return dt.strftime("%Y-%m-%d %H:%M")
    return date_string


def get_recent_records():
    """获取昨天8:30之后的记录"""
    try:
        data_table = Document.Data.Tables["新品占线时长"]
        data_table.ReloadAllData()

        # 按顺序定义所需列
        column_names = [
            "线体",
            "制程",
            "产品料号",
            "产品描述",
            "状态",
            "当前总耗时(h)",
            "前产品ULD时间",
            "本产品ULD时间"
        ]

        # 创建列游标字典
        cursors = {}
        for col_name in column_names:
            cursors[col_name] = DataValueCursor.CreateFormatted(data_table.Columns[col_name])

        records = []
        rows_to_include = IndexSet(data_table.RowCount, True)

        # 计算昨天8:30的时间点
        yesterday = datetime.now() - timedelta(days=1)
        yesterday_830 = yesterday.replace(hour=8, minute=30, second=0, microsecond=0)

        # 收集唯一的线体值和制程值
        unique_lines = set()
        unique_processes = set()

        # 使用GetRows获取所有列的数据
        for row in data_table.GetRows(rows_to_include, *cursors.values()):
            # 处理本产品ULD时间
            uld_time_str = cursors["本产品ULD时间"].CurrentValue
            normalized_uld_time = normalize_datetime_format(uld_time_str)
            uld_time = datetime.strptime(normalized_uld_time + ":00", "%Y-%m-%d %H:%M:%S")

            # 处理前产品ULD时间
            prev_uld_time_str = cursors["前产品ULD时间"].CurrentValue
            normalized_prev_uld_time = normalize_datetime_format(prev_uld_time_str)

            if uld_time >= yesterday_830:
                line_value = cursors["线体"].CurrentValue
                process_value = cursors["制程"].CurrentValue
                unique_lines.add(line_value)
                unique_processes.add(process_value)

                record = {
                    'line': line_value,
                    'process': process_value,
                    'product_code': cursors["产品料号"].CurrentValue,
                    'product_desc': cursors["产品描述"].CurrentValue,
                    'status': cursors["状态"].CurrentValue,
                    'duration': float(cursors["当前总耗时(h)"].CurrentValue),
                    'prev_uld_time': normalized_prev_uld_time,  # 使用格式化后的时间
                    'uld_time': normalized_uld_time  # 使用格式化后的时间
                }
                records.append(record)

        # 为线体和制程创建颜色映射
        line_colors = ['#FFB6C1', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA']
        line_colors_map = {line: line_colors[i % len(line_colors)] for i, line in enumerate(sorted(unique_lines))}

        # 为制程使用不同色系的颜色
        process_colors = ['#FFA07A', '#90EE90', '#ADD8E6', '#D8BFD8', '#FAFAD2', '#B0C4DE']
        process_color_map = {process: process_colors[i % len(process_colors)] for i, process in
                             enumerate(sorted(unique_processes))}

        # 添加颜色信息到记录中
        for record in records:
            record['line_color'] = line_colors_map[record['line']]
            record['process_color'] = process_color_map[record['process']]

        return records

    except Exception as e:
        print("Get records failed: {}".format(str(e)))
        return None


def create_email_message(records):
    """创建邮件内容"""
    try:
        message = MIMEMultipart()

        # 创建HTML表格样式
        html_content = """
        <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; }
                    table { 
                        border-collapse: collapse; 
                        width: 100%;
                        margin-top: 20px;
                    }
                    th { 
                        background-color: #4A90E2; 
                        color: white; 
                        padding: 8px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td { 
                        padding: 8px; 
                        border: 1px solid #ddd; 
                    }
                    .status-running { 
                        background-color: #FF9999; 
                    }
                    .status-running { 
                        color: red;  /* 红色字体 */
                    }
                    .status-completed { 
                        background-color: #E8FFE8;  /* 浅绿色背景 */
                    }
                    .uld-time { 
                        background-color: #E8E8E8; 
                    }
                    .note {
                        color: #666;
                        font-size: 13px;
                        margin: 5px 0;
                        line-height: 1.4;
                    }
                    .notes-container {
                        background-color: #f9f9f9;
                        padding: 10px;
                        border-left: 3px solid #4A90E2;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <h3>近1日CF新品耗时情况数据：</h3>
                <table>
                    <thead>
                        <tr>
                            <th>线体</th>
                            <th>制程</th>
                            <th>产品料号</th>
                            <th>产品描述</th>
                            <th>状态</th>
                            <th>时间跨度(h)</th>
                            <th>前产品ULD时间</th>
                            <th>本产品ULD时间</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # 计算当前总耗时的最大值和最小值用于颜色渐变
        max_duration = max(record['duration'] for record in records)
        min_duration = min(record['duration'] for record in records)
        duration_range = max_duration - min_duration

        # 添加数据行
        for record in records:
            # 计算当前总耗时的颜色深度（使用HSL颜色格式）
            # 将值映射到40%~90%的亮度范围（L值），较大的值显示较深的颜色
            if duration_range == 0:
                lightness = 70  # 如果所有值相等，使用中等亮度
            else:
                normalized_value = (record['duration'] - min_duration) / duration_range
                lightness = 90 - (normalized_value * 50)  # 40%->100%的渐变

            duration_color = "hsl(145, 50%, {}%)".format(lightness)

            # 设置状态和ULD时间的样式类
            status_class = ''
            if record['status'] == 'Run货中':
                status_class = ' class="status-running"'
            else:
                status_class = ' class="status-completed"'
            uld_class = ' class="uld-time"' if record['status'] == 'Run货中' else ''

            row_html = """
                <tr>
                    <td style="background-color: {line_color}">{line}</td>
                    <td style="background-color: {process_color}">{process}</td>
                    <td>{product_code}</td>
                    <td>{product_desc}</td>
                    <td{status_class}>{status}</td>
                    <td style="background-color: {duration_color}">{duration:.1f}</td>
                    <td>{prev_uld_time}</td>
                    <td{uld_class}>{uld_time}</td>
                </tr>
            """.format(
                line_color=record['line_color'],
                line=record['line'],
                process_color=record['process_color'],
                process=record['process'],
                product_code=record['product_code'],
                product_desc=record['product_desc'],
                status_class=status_class,
                status=record['status'],
                duration_color=duration_color,
                duration=record['duration'],
                prev_uld_time=record['prev_uld_time'],
                uld_class=uld_class,
                uld_time=record['uld_time']
            )

            html_content += row_html

        html_content += """
                    </tbody>
                </table>
                <div class="notes-container">
                    <p class="note">说明：</p>
                    <p class="note">① 时间跨度：前产品出Unload口时间~本产品出Unload口时间</p>
                    <p class="note">② 若Run货前线体长时间空闲（如设备保养等），会导致时间跨度计算失真</p>
                    <p class="note">③ "Run货中"状态表示尚无后续产品从当前线体Unload口产出。若当前产品Run完后线体IDLE，会导致处于"Run货中"状态。可通过"本产品ULD时间"综合判断</p>
                </div>
                <p style="color: #666; font-size: 12px;">本邮件为自动发送，无需回复</p>
            </body>
        </html>
        """

        message.attach(MIMEText(html_content, "html", "utf-8"))

        # 设置邮件头部信息
        # message["From"] = SENDER
        # message["To"] = ", ".join(RECEIVERS)

        message["From"] = formataddr((Header(SENDER_NAME), SENDER))

        # 如果有多个收件人需要显示名称，需要分别构建
        recipients = []
        for i, receiver in enumerate(RECEIVERS):
            if i < len(RECEIVERS_NAMES):
                recipients.append(formataddr((Header(RECEIVERS_NAMES[i]), receiver)))
            else:
                recipients.append(receiver)

        message["To"] = ", ".join(recipients)

        date_str = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        full_subject = "{} - {}".format(Header("CF新品耗时日报"), date_str)
        message["Subject"] = full_subject

        return message

    except Exception as e:
        print("Create message failed: {}".format(e))
        return None


def send_email(records):
    """发送电子邮件"""
    try:
        # 建立SMTP连接
        smtp_obj = smtplib.SMTP(MAIL_HOST, MAIL_PORT)
        smtp_obj.starttls()
        smtp_obj.ehlo_or_helo_if_needed()

        # 创建邮件消息
        message = create_email_message(records)
        if not message:
            return False

        # 发送邮件
        smtp_obj.sendmail(SENDER, RECEIVERS, message.as_string())
        print("Email sent successfully")
        return True

    except Exception as e:
        print("Send email failed: {}".format(e))
        return False

    finally:
        try:
            smtp_obj.quit()
        except:
            pass


def main():
    """主函数"""
    try:
        print("Starting execution...")

        # 获取记录
        records = get_recent_records()
        if not records or len(records) == 0:
            print("No records found after yesterday 8:30")
            return

        print("Found {} records".format(len(records)))

        # 发送邮件
        if send_email(records):
            print("Process completed successfully")
        else:
            print("Failed to send email")

    except Exception as e:
        print("Execution failed: {}".format(e))


# 执行主函数
main()
